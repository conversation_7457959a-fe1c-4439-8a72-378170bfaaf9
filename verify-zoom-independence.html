<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zoom-Independent Service Images Verification</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            background: linear-gradient(135deg, #0a1628 0%, #1e293b 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .verification-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: rgba(28, 28, 30, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .zoom-controls {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .zoom-btn {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(6, 182, 212, 0.6));
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .zoom-btn:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 1), rgba(6, 182, 212, 0.8));
            transform: translateY(-2px);
        }
        
        .zoom-btn.active {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.8), rgba(16, 185, 129, 0.6));
            border-color: rgba(34, 197, 94, 0.5);
        }
        
        .test-results {
            background: rgba(15, 23, 42, 0.6);
            border-radius: 12px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        .info { color: #3b82f6; }
        
        .sample-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 24px;
        }
        
        /* Import the actual service image styles */
        .suz-service-image-container {
            position: relative;
            overflow: hidden;
            border-radius: 1rem;
            background: linear-gradient(135deg, rgba(10, 132, 255, 0.1), rgba(100, 210, 255, 0.05));
            border: 1px solid rgba(10, 132, 255, 0.2);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            box-shadow:
                0 8px 25px rgba(10, 132, 255, 0.1),
                0 4px 12px rgba(100, 210, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-block;
            width: 100%;
            min-height: clamp(200px, 16vw, 320px);
            max-height: clamp(240px, 20vw, 400px);
            contain: layout style paint;
            transform: translateZ(0);
        }
        
        .suz-service-image {
            width: 100%;
            height: 100%;
            min-height: clamp(200px, 16vw, 320px);
            max-height: clamp(240px, 20vw, 400px);
            object-fit: contain;
            object-position: center;
            border-radius: 1rem;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            filter: brightness(0.9) contrast(1.1) saturate(1.1);
            aspect-ratio: auto;
            transform: translateZ(0);
            will-change: transform, filter;
            contain: layout style paint;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
        }
        
        .instructions {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #60a5fa;
        }
        
        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <h1>🔍 Zoom-Independent Service Images Verification</h1>
        
        <div class="instructions">
            <h3>📋 Testing Instructions</h3>
            <ol>
                <li>Use the zoom buttons below or browser zoom (Ctrl/Cmd + +/-)</li>
                <li>Observe that the sample images maintain consistent visual size</li>
                <li>Check the test results for technical measurements</li>
                <li>Verify that glass morphism effects remain intact</li>
                <li>Test on different screen sizes and browsers</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>🎛️ Zoom Controls</h2>
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="setZoom(0.5)">50%</button>
                <button class="zoom-btn" onclick="setZoom(0.75)">75%</button>
                <button class="zoom-btn active" onclick="setZoom(1.0)">100%</button>
                <button class="zoom-btn" onclick="setZoom(1.25)">125%</button>
                <button class="zoom-btn" onclick="setZoom(1.5)">150%</button>
                <button class="zoom-btn" onclick="setZoom(2.0)">200%</button>
                <button class="zoom-btn" onclick="runTest()">🧪 Run Test</button>
            </div>
            
            <div class="test-results" id="testResults">
                <div class="info">Click "Run Test" to measure image dimensions at current zoom level.</div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🖼️ Sample Service Images</h2>
            <div class="sample-images">
                <div class="suz-service-image-container">
                    <img class="suz-service-image" 
                         src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDQwMCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjQwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8dGV4dCB4PSIyMDAiIHk9IjEyMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkhvdGVsemltbWVycmVpbmlndW5nPC90ZXh0Pgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudCIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiMzYjgyZjYiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDZiNmQ0Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K" 
                         alt="Hotelzimmerreinigung Test Image">
                </div>
                
                <div class="suz-service-image-container">
                    <img class="suz-service-image" 
                         src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDQwMCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjQwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8dGV4dCB4PSIyMDAiIHk9IjEyMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkJ1ZXJvcmVpbmlndW5nPC90ZXh0Pgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudCIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM4YjVjZjYiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDZiNmQ0Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K" 
                         alt="Bueroreinigung Test Image">
                </div>
                
                <div class="suz-service-image-container">
                    <img class="suz-service-image" 
                         src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDQwMCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjQwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8dGV4dCB4PSIyMDAiIHk9IjEyMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZlbnN0ZXJyZWluaWd1bmc8L3RleHQ+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzEwYjk4MSIvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwNmI2ZDQiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K" 
                         alt="Fensterreinigung Test Image">
                </div>
            </div>
        </div>
    </div>

    <script>
        let testData = {};
        
        function setZoom(level) {
            document.body.style.zoom = level;
            
            // Update active button
            document.querySelectorAll('.zoom-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Auto-run test after zoom change
            setTimeout(runTest, 100);
        }
        
        function runTest() {
            const results = document.getElementById('testResults');
            const images = document.querySelectorAll('.suz-service-image');
            const containers = document.querySelectorAll('.suz-service-image-container');
            
            if (images.length === 0) {
                results.innerHTML = '<div class="error">❌ No service images found for testing</div>';
                return;
            }
            
            const currentZoom = parseFloat(document.body.style.zoom) || 1.0;
            const zoomPercent = Math.round(currentZoom * 100);
            
            let output = `<div class="info">🔍 Testing at ${zoomPercent}% zoom level</div>\n`;
            output += `<div class="info">📊 Found ${images.length} service images</div>\n\n`;
            
            const measurements = [];
            
            images.forEach((img, index) => {
                const container = containers[index];
                const imgRect = img.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                const computedStyle = getComputedStyle(img);
                
                const measurement = {
                    index: index + 1,
                    image: {
                        width: Math.round(imgRect.width),
                        height: Math.round(imgRect.height)
                    },
                    container: {
                        width: Math.round(containerRect.width),
                        height: Math.round(containerRect.height)
                    },
                    computed: {
                        minHeight: computedStyle.minHeight,
                        maxHeight: computedStyle.maxHeight,
                        transform: computedStyle.transform,
                        contain: computedStyle.contain
                    }
                };
                
                measurements.push(measurement);
                
                output += `<div class="success">📸 Image ${index + 1}:</div>`;
                output += `   Size: ${measurement.image.width}×${measurement.image.height}px\n`;
                output += `   Container: ${measurement.container.width}×${measurement.container.height}px\n`;
                output += `   Min Height: ${measurement.computed.minHeight}\n`;
                output += `   Max Height: ${measurement.computed.maxHeight}\n\n`;
            });
            
            // Store measurements for comparison
            testData[zoomPercent] = measurements;
            
            // Compare with other zoom levels
            const zoomLevels = Object.keys(testData).map(Number).sort((a, b) => a - b);
            if (zoomLevels.length > 1) {
                output += `<div class="info">📊 ZOOM COMPARISON:</div>\n`;
                
                const baseZoom = zoomLevels[0];
                const baseMeasurements = testData[baseZoom];
                
                let allConsistent = true;
                
                zoomLevels.forEach(zoom => {
                    if (zoom === baseZoom) return;
                    
                    const currentMeasurements = testData[zoom];
                    output += `\n<div class="info">🔍 ${baseZoom}% vs ${zoom}%:</div>\n`;
                    
                    baseMeasurements.forEach((baseMeasurement, index) => {
                        const currentMeasurement = currentMeasurements[index];
                        
                        const widthDiff = Math.abs(baseMeasurement.image.width - currentMeasurement.image.width);
                        const heightDiff = Math.abs(baseMeasurement.image.height - currentMeasurement.image.height);
                        
                        const isConsistent = widthDiff <= 2 && heightDiff <= 2; // 2px tolerance
                        
                        if (!isConsistent) {
                            allConsistent = false;
                            output += `   <div class="error">❌ Image ${index + 1}: Size changed significantly</div>\n`;
                            output += `      ${baseZoom}%: ${baseMeasurement.image.width}×${baseMeasurement.image.height}px\n`;
                            output += `      ${zoom}%: ${currentMeasurement.image.width}×${currentMeasurement.image.height}px\n`;
                        } else {
                            output += `   <div class="success">✅ Image ${index + 1}: Consistent (±${Math.max(widthDiff, heightDiff)}px)</div>\n`;
                        }
                    });
                });
                
                if (allConsistent && zoomLevels.length > 1) {
                    output += `\n<div class="success">🎉 SUCCESS: All images maintain zoom-independent sizing!</div>\n`;
                }
            }
            
            // Test browser support
            output += `\n<div class="info">🌐 BROWSER SUPPORT:</div>\n`;
            output += `   clamp(): ${CSS.supports('min-height', 'clamp(200px, 16vw, 320px)') ? '✅' : '❌'}\n`;
            output += `   contain: ${CSS.supports('contain', 'layout style paint') ? '✅' : '❌'}\n`;
            output += `   backdrop-filter: ${CSS.supports('backdrop-filter', 'blur(10px)') ? '✅' : '❌'}\n`;
            
            results.innerHTML = `<pre>${output}</pre>`;
        }
        
        // Run initial test
        setTimeout(runTest, 500);
    </script>
</body>
</html>
